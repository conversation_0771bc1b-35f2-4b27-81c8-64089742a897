import { FC, ReactNode, useEffect } from "react";
import { Navigate, Route, Routes, useNavigate } from "react-router-dom";
import Auth from "./authentication/auth";
import Login from "./authentication/login";
import { ETierAppliedCollectionType } from "./components/enums/tier-applied-collection-type.ts";
import Error403 from "./components/errors/error403.tsx";
import Error404 from "./components/errors/error404.tsx";
import { LoadingOverlay } from "./components/loading/loading-overlay.tsx";
import Analytics from "./container/dashboards/analytics/analytics.tsx";
import DashboardClassified from "./container/dashboards/classified/classified.tsx";
import Ecommerce from "./container/dashboards/ecommerce/ecommerce.tsx";
import ManagementAdminDetails from "./container/managements/admin/admin_details_table.tsx";
import ManagementAdmin from "./container/managements/admin/admin_table.tsx";
import AffiliateDetail from "./container/managements/affiliation/affiliate-details.tsx";
import AffiliatePayout from "./container/managements/affiliation/affiliate-payouts.tsx";
import AffiliateTierDetail from "./container/managements/affiliation/affiliate-tier-detail.tsx";
import AffiliateTiers from "./container/managements/affiliation/affiliate-tiers.tsx";
import CommissionDetail from "./container/managements/affiliation/affiliation-commission-details.tsx";
import AffiliationCommissions from "./container/managements/affiliation/affiliation-commissions.tsx";
import AppliedProductsSelector from "./container/managements/affiliation/applied-products-selector.tsx";
import ManagementAffiliateRegistration from "./container/managements/affiliation/management-affiliate-registration.tsx";
import ManagementAffiliates from "./container/managements/affiliation/management-affiliates.tsx";
import TierAppliedProducts from "./container/managements/affiliation/tier-applied-products.tsx";
import ManagementCancelReasons from "./container/managements/cancel-reasons/cancel_reason_table.tsx";
import ManagementContactInformation from "./container/managements/contact-information/contact_information_table.tsx";
import ManagementDrawerMenu from "./container/managements/drawer-menu/drawer_menu_table.tsx";
import ManagementCampaignDetails from "./container/managements/marketing/campaign/campaign_details_table.tsx";
import ManagementCampaign from "./container/managements/marketing/campaign/campaign_table.tsx";
import ManagementCoupon from "./container/managements/marketing/coupon/management_coupon.tsx";
import EventDetail from "./container/managements/marketing/event/event_detail.tsx";
import EventForm from "./container/managements/marketing/event/event_form.tsx";
import ManagementEvent from "./container/managements/marketing/event/event_table.tsx";
import ManagementGiftDetails from "./container/managements/marketing/gift/gift_details_table.tsx";
import ManagementGift from "./container/managements/marketing/gift/gift_table.tsx";
import ManagementRewardPoint from "./container/managements/marketing/reward-point/manage-reward-point.tsx";
import SalonConstructionServiceTable from "./container/managements/marketing/salon-construction-service/salon_construction_service_table.tsx";
import SalonConstructionServiceDetails from "./container/managements/marketing/salon-construction-service/salon_construction_service_details.tsx";
import ManagementNailSystem from "./container/managements/nail-system/nail_system_table.tsx";
import ZurnoNotification from "./container/managements/notifications/zurno_notification_table.tsx";
import ManagementOrderDetails from "./container/managements/order/order/order_details.tsx";
import ManagementOrder from "./container/managements/order/order/order_list.tsx";
import ManagementPermissionsDetails from "./container/managements/permission/permission_details.tsx";
import ManagementPermissions from "./container/managements/permission/permission_groups.tsx";
import CategoryDetails from "./container/managements/product/category/category_details";
import ManagementProductCategory from "./container/managements/product/category/manage_product_category.tsx";
import ManagementCollectionDetails from "./container/managements/product/collection/collection_details.tsx";
import ManagementCollectionList from "./container/managements/product/collection/collection_list.tsx";
import ManagementProductOrganization from "./container/managements/product/product-organization/product_organization.tsx";
import ManagementProductDetails from "./container/managements/product/product/product_details.tsx";
import ManagementProductList from "./container/managements/product/product/product_list.tsx";
import ManagementRoleDetails from "./container/managements/roles/role_details_table.tsx";
import ManagementRole from "./container/managements/roles/role_table.tsx";
import Settings from "./container/managements/settings/settings.tsx";
import ManagementShopCategory from "./container/managements/shop-category/index.tsx";
import ManagementShopCollection from "./container/managements/shop-collection/index.tsx";
import ManagementClaimStoreRequest from "./container/managements/store/claim-store/claim_store_table.tsx";
import ManagementUserDetails from "./container/managements/user/user_details_table.tsx";
import ManagementUser from "./container/managements/user/user_table.tsx";
import App from "./pages/App";
import { ClassifiedRouter } from "./routers/classified/index.tsx";
import { HomeLayoutRouter } from "./routers/home-layout/index.tsx";
import { ReportRouter } from "./routers/report/index.tsx";
import { StoreRouter } from "./routers/store/index.tsx";
import { useLazyGetPermissionsByAdminIdQuery } from "./services/admin.ts";
import { useLazyGetProfileQuery } from "./services/profile.ts";
import { useAppDispatch } from "./services/rtk/hooks.ts";
import { store } from "./services/rtk/store.ts";
import { setUser } from "./services/slices/auth.slice.ts";
import { setPermissions } from "./services/slices/authorization.slice.ts";
import {
  hasPermission,
  hasSuperAdminPermission,
} from "./utils/authorization.ts";
import { ACTION, RESOURCE } from "./utils/constant/authorization.ts";
import { ACCESS_TOKEN, ROLE } from "./utils/constant/local-storage";
import AffiliateUserPicker from "./container/managements/affiliation/affiliate-user-picker.tsx";
import TierCommissionableProducts from "./container/managements/affiliation/tier-commissionable-products.tsx";
import ManagementRewardProgram from "./container/managements/marketing/reward-program/manage_reward_program.tsx";
import ManagementRewardProgramDetails from "./container/managements/marketing/reward-program/manage_reward_program_details.tsx";
import VendorsList from "./container/managements/vendors/vendors-list.tsx";
import VendorDetail from "./container/managements/vendors/vendor-detail.tsx";
import VendorEdit from "./container/managements/vendors/vendor-edit.tsx";
import VendorsApprovalList from "./container/managements/vendors/vendors-approval-list.tsx";
import VendorsSettings from "./container/managements/vendors/vendors-settings.tsx";
import VendorEarningsList from "./container/managements/vendors/vendor-earnings-list.tsx";
import VendorEarningDetail from "./container/managements/vendors/vendor-earning-details.tsx";
import ManagementProductApprovalList from "./container/managements/product/product-approval/product_approval_list.tsx";
import ManagementProductApprovalDetails from "./container/managements/product/product-approval/product_approval_details.tsx";
import Chatbot from "./container/dashboards/chatbot/chatbot.tsx";

const RequireAuth: FC<{ children: ReactNode }> = ({ children }) => {
  const token = localStorage.getItem(ACCESS_TOKEN);
  const user = store.getState().auth.user;

  const [getProfile] = useLazyGetProfileQuery();
  const [getPermissions, { isLoading }] = useLazyGetPermissionsByAdminIdQuery();
  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  if (!token) {
    return <Navigate to={`${import.meta.env.BASE_URL}`} replace />;
  }

  useEffect(() => {
    if (!user) {
      getProfile().then((res) => {
        if (res.data && (res.data as TAdmin)) {
          dispatch(setUser(res.data));
        }
      });
    } else {
      if (!user.isActive) {
        localStorage.clear();
        navigate("/");
      }

      user.isSuperAdmin
        ? localStorage.setItem(ROLE, "super_admin")
        : localStorage.setItem(ROLE, "admin");

      getPermissions(user.id).then((res) => {
        if (res.data && (res.data as TPermission[])) {
          dispatch(setPermissions(res.data));
        }
      });
    }
  }, [user]);

  if (isLoading) return <LoadingOverlay />;

  return <>{children}</>;
};

export const AuthorizedRoute: FC<{
  resource?: RESOURCE;
  children: ReactNode;
}> = ({ resource, children }) => {
  if (!resource) {
    return hasSuperAdminPermission() ? <>{children}</> : <Error403 />;
  } else {
    return hasPermission(ACTION.READ, resource) ? (
      <>{children}</>
    ) : (
      <Error403 />
    );
  }
};

export const Routing = () => {
  return (
    <Routes>
      <Route path={`${import.meta.env.BASE_URL}`} element={<Auth />}>
        <Route index element={<Login />} />
      </Route>

      <Route
        path={`${import.meta.env.BASE_URL}`}
        element={
          <RequireAuth>
            <App />
          </RequireAuth>
        }
      >
        <Route path={`settings`} element={<Settings />} />

        <Route
          path={`${import.meta.env.BASE_URL}dashboards-ecommerce`}
          element={<Ecommerce />}
        />
        <Route
          path={`${import.meta.env.BASE_URL}dashboards/marketplace`}
          element={<DashboardClassified />}
        />
        <Route
          path={`${import.meta.env.BASE_URL}dashboards-analytics`}
          element={<Analytics />}
        />

        <Route
          path={`managements-products-approval`}
          element={
            <AuthorizedRoute resource={RESOURCE.PRODUCT}>
              <ManagementProductApprovalList />
            </AuthorizedRoute>
          }
        />
        <Route
          path={`managements-products-approval/:id`}
          element={
            <AuthorizedRoute resource={RESOURCE.PRODUCT}>
              <ManagementProductApprovalDetails />
            </AuthorizedRoute>
          }
        />
        <Route
          path={`managements-products`}
          element={
            <AuthorizedRoute resource={RESOURCE.PRODUCT}>
              <ManagementProductList />
            </AuthorizedRoute>
          }
        />
        <Route
          path={`managements-products/:id?`}
          element={
            <AuthorizedRoute resource={RESOURCE.PRODUCT}>
              <ManagementProductDetails />
            </AuthorizedRoute>
          }
        />
        <Route
          path={`managements-product-category`}
          element={
            <AuthorizedRoute resource={RESOURCE.PRODUCT}>
              <ManagementProductCategory />
            </AuthorizedRoute>
          }
        />
        <Route
          path={`managements-products-organization`}
          element={
            <AuthorizedRoute resource={RESOURCE.PRODUCT}>
              <ManagementProductOrganization />
            </AuthorizedRoute>
          }
        />
        <Route
          path={`managements-collections`}
          element={
            <AuthorizedRoute resource={RESOURCE.COLLECTION}>
              <ManagementCollectionList />
            </AuthorizedRoute>
          }
        />
        <Route
          path={`managements-collections/:id?`}
          element={
            <AuthorizedRoute resource={RESOURCE.COLLECTION}>
              <ManagementCollectionDetails />
            </AuthorizedRoute>
          }
        />

        <Route
          path={`managements-users`}
          element={
            <AuthorizedRoute resource={RESOURCE.USER}>
              <ManagementUser />
            </AuthorizedRoute>
          }
        />
        <Route
          path={`managements-users/details/:id?/:edit?`}
          element={
            <AuthorizedRoute resource={RESOURCE.USER}>
              <ManagementUserDetails />
            </AuthorizedRoute>
          }
        />
        <Route
          path={`managements-marketplace/*`}
          element={<ClassifiedRouter />}
        />

        <Route path={`managements-report/*`} element={<ReportRouter />} />

        <Route
          path={`managements-campaigns`}
          element={
            <AuthorizedRoute resource={RESOURCE.CAMPAIGN}>
              <ManagementCampaign />
            </AuthorizedRoute>
          }
        />
        <Route
          path={`managements-campaigns/:id`}
          element={
            <AuthorizedRoute resource={RESOURCE.CAMPAIGN}>
              <ManagementCampaignDetails />
            </AuthorizedRoute>
          }
        />
        <Route
          path={`managements-cancel-reasons`}
          element={
            <AuthorizedRoute resource={RESOURCE.CANCEL_REASON}>
              <ManagementCancelReasons />
            </AuthorizedRoute>
          }
        />
        <Route
          path={`managements-contact-informations`}
          element={
            <AuthorizedRoute resource={RESOURCE.CONTACT_INFORMATION}>
              <ManagementContactInformation />
            </AuthorizedRoute>
          }
        />
        <Route
          path={`managements-drawer-menus`}
          element={
            <AuthorizedRoute resource={RESOURCE.DRAWER_MENU}>
              <ManagementDrawerMenu />
            </AuthorizedRoute>
          }
        />
        <Route
          path="managements-home-layout/*"
          element={<HomeLayoutRouter />}
        />

        <Route
          path={`managements-nail-systems`}
          element={
            <AuthorizedRoute resource={RESOURCE.NAIL_SYSTEM}>
              <ManagementNailSystem />
            </AuthorizedRoute>
          }
        />
        <Route
          path={`managements-store/claim-store`}
          element={<ManagementClaimStoreRequest />}
        />
        <Route
          path={`managements-reward-points`}
          element={<ManagementRewardPoint />}
        />
        <Route
          path={`managements-reward-programs`}
          element={<ManagementRewardProgram />}
        />
        <Route
          path={`managements-reward-programs/:id`}
          element={<ManagementRewardProgramDetails />}
        />

        <Route path="managements-store/*" element={<StoreRouter />} />

        <Route
          path={`managements-admins`}
          element={
            <AuthorizedRoute resource={RESOURCE.ADMIN}>
              <ManagementAdmin />
            </AuthorizedRoute>
          }
        />
        <Route
          path={`managements-admins/details/:id?/:edit?`}
          element={
            <AuthorizedRoute resource={RESOURCE.ADMIN}>
              <ManagementAdminDetails />
            </AuthorizedRoute>
          }
        />
        <Route
          path={`managements-roles`}
          element={
            <AuthorizedRoute resource={RESOURCE.ROLE}>
              <ManagementRole />
            </AuthorizedRoute>
          }
        />
        <Route
          path={`managements-roles/:id?`}
          element={
            <AuthorizedRoute resource={RESOURCE.ROLE}>
              <ManagementRoleDetails />
            </AuthorizedRoute>
          }
        />
        <Route
          path={`managements-permissions-groups`}
          element={
            <AuthorizedRoute>
              <ManagementPermissions />
            </AuthorizedRoute>
          }
        />
        <Route
          path={`managements-permissions-groups/:id?`}
          element={
            <AuthorizedRoute>
              <ManagementPermissionsDetails />
            </AuthorizedRoute>
          }
        />
        <Route
          path={`managements-order`}
          element={
            <AuthorizedRoute resource={RESOURCE.ORDER}>
              <ManagementOrder />
            </AuthorizedRoute>
          }
        />
        <Route
          path={`managements-order/:id?`}
          element={
            <AuthorizedRoute resource={RESOURCE.ORDER}>
              <ManagementOrderDetails />
            </AuthorizedRoute>
          }
        />
        <Route
          path={`managements-coupon`}
          element={
            <AuthorizedRoute resource={RESOURCE.COUPON}>
              <ManagementCoupon />
            </AuthorizedRoute>
          }
        />

        <Route
          path={`managements-gift`}
          element={
            <AuthorizedRoute resource={RESOURCE.GIFT}>
              <ManagementGift />
            </AuthorizedRoute>
          }
        />

        <Route
          path={`managements-gift/details/:giftId?/:edit?`}
          element={
            <AuthorizedRoute resource={RESOURCE.GIFT}>
              <ManagementGiftDetails />
            </AuthorizedRoute>
          }
        />

        <Route
          path={`managements-salon-construction-service`}
          element={
            <AuthorizedRoute resource={RESOURCE.SALON_CONSTRUCTION_SERVICE}>
              <SalonConstructionServiceTable />
            </AuthorizedRoute>
          }
        />

        <Route
          path={`managements-salon-construction-service/details/:id`}
          element={
            <AuthorizedRoute resource={RESOURCE.SALON_CONSTRUCTION_SERVICE}>
              <SalonConstructionServiceDetails />
            </AuthorizedRoute>
          }
        />

        <Route path={`managements-report/*`} element={<ReportRouter />} />

        <Route
          path={`managements-product-categories`}
          element={
            <AuthorizedRoute resource={RESOURCE.PRODUCT}>
              <ManagementProductCategory />
            </AuthorizedRoute>
          }
        />
        <Route
          path={`managements-product-categories/:id`}
          element={
            <AuthorizedRoute resource={RESOURCE.PRODUCT}>
              <CategoryDetails />
            </AuthorizedRoute>
          }
        />

        <Route
          path={`managements-affiliate-tiers`}
          element={
            <AuthorizedRoute resource={RESOURCE.AFFILIATION}>
              <AffiliateTiers />
            </AuthorizedRoute>
          }
        />

        <Route
          path={`managements-affiliate-tiers/:id`}
          element={
            <AuthorizedRoute resource={RESOURCE.AFFILIATION}>
              <AffiliateTierDetail />
            </AuthorizedRoute>
          }
        />

        <Route
          path={`managements-affiliate-tiers/:tierId/commission-products`}
          element={
            <AuthorizedRoute resource={RESOURCE.AFFILIATION}>
              <TierCommissionableProducts />
            </AuthorizedRoute>
          }
        />

        <Route
          path={`managements-affiliate-tiers/:tierId/commission-products/selector`}
          element={
            <AuthorizedRoute resource={RESOURCE.AFFILIATION}>
              <AppliedProductsSelector
                collectionType={ETierAppliedCollectionType.COMMISSION}
              />
            </AuthorizedRoute>
          }
        />

        <Route
          path={`managements-affiliate-tiers/:tierId/discount-products`}
          element={
            <AuthorizedRoute resource={RESOURCE.AFFILIATION}>
              <TierAppliedProducts
                collectionType={ETierAppliedCollectionType.DISCOUNT}
              />
            </AuthorizedRoute>
          }
        />

        <Route
          path={`managements-affiliate-tiers/:tierId/discount-products/selector`}
          element={
            <AuthorizedRoute resource={RESOURCE.AFFILIATION}>
              <AppliedProductsSelector
                collectionType={ETierAppliedCollectionType.DISCOUNT}
              />
            </AuthorizedRoute>
          }
        />

        <Route
          path={`managements-affiliates`}
          element={
            <AuthorizedRoute resource={RESOURCE.AFFILIATION}>
              <ManagementAffiliates />
            </AuthorizedRoute>
          }
        />

        <Route
          path={`managements-affiliates/:id/payouts`}
          element={
            <AuthorizedRoute resource={RESOURCE.AFFILIATION}>
              <AffiliatePayout />
            </AuthorizedRoute>
          }
        />

        <Route
          path={`managements-affiliates/:id`}
          element={
            <AuthorizedRoute resource={RESOURCE.AFFILIATION}>
              <AffiliateDetail />
            </AuthorizedRoute>
          }
        />

        <Route
          path={`managements-affiliate-registration/users`}
          element={
            <AuthorizedRoute resource={RESOURCE.AFFILIATION}>
              <AffiliateUserPicker />
            </AuthorizedRoute>
          }
        />

        <Route
          path={`managements-affiliate-registration`}
          element={
            <AuthorizedRoute resource={RESOURCE.AFFILIATION}>
              <ManagementAffiliateRegistration />
            </AuthorizedRoute>
          }
        />

        <Route
          path={`commission-details/:id`}
          element={
            <AuthorizedRoute resource={RESOURCE.AFFILIATION}>
              <CommissionDetail />
            </AuthorizedRoute>
          }
        />

        <Route
          path={`commissions`}
          element={
            <AuthorizedRoute resource={RESOURCE.AFFILIATION}>
              <AffiliationCommissions />
            </AuthorizedRoute>
          }
        />

        <Route
          path={`managements-shop-categories`}
          element={
            <AuthorizedRoute resource={RESOURCE.SHOP_CATEGORY}>
              <ManagementShopCategory />
            </AuthorizedRoute>
          }
        />
        <Route
          path={`managements-shop-collections`}
          element={
            <AuthorizedRoute resource={RESOURCE.SHOP_COLLECTION}>
              <ManagementShopCollection />
            </AuthorizedRoute>
          }
        />

        <Route path={`notifications`} element={<ZurnoNotification />} />

        <Route
          path={`managements-event`}
          element={
            <AuthorizedRoute resource={RESOURCE.EVENT}>
              <ManagementEvent />
            </AuthorizedRoute>
          }
        />
        <Route
          path={`managements-event/new`}
          element={
            <AuthorizedRoute resource={RESOURCE.EVENT}>
              <EventForm />
            </AuthorizedRoute>
          }
        />
        <Route
          path={`managements-event/:id`}
          element={
            <AuthorizedRoute resource={RESOURCE.EVENT}>
              <EventDetail />
            </AuthorizedRoute>
          }
        />
        <Route
          path={`managements-event/:id/edit`}
          element={
            <AuthorizedRoute resource={RESOURCE.EVENT}>
              <EventForm />
            </AuthorizedRoute>
          }
        />

        <Route
          path={`managements-vendors`}
          element={
            <AuthorizedRoute resource={RESOURCE.EVENT}>
              <VendorsList />
            </AuthorizedRoute>
          }
        />

        <Route
          path={`managements-vendors-approval`}
          element={
            <AuthorizedRoute resource={RESOURCE.EVENT}>
              <VendorsApprovalList />
            </AuthorizedRoute>
          }
        />

        <Route
          path="/managements-vendors/:id"
          element={
            <AuthorizedRoute resource={RESOURCE.EVENT}>
              <VendorDetail />
            </AuthorizedRoute>
          }
        />

        <Route
          path="/managements-vendors/:id/edit"
          element={
            <AuthorizedRoute resource={RESOURCE.EVENT}>
              <VendorEdit />
            </AuthorizedRoute>
          }
        />

        <Route
          path={`managements-vendor-earnings`}
          element={
            <AuthorizedRoute resource={RESOURCE.EVENT}>
              <VendorEarningsList />
            </AuthorizedRoute>
          }
        />

        <Route
          path={`managements-vendor-earnings/:id`}
          element={
            <AuthorizedRoute resource={RESOURCE.EVENT}>
              <VendorEarningDetail />
            </AuthorizedRoute>
          }
        />

        <Route
          path={`managements-vendors-settings`}
          element={
            <AuthorizedRoute resource={RESOURCE.EVENT}>
              <VendorsSettings />
            </AuthorizedRoute>
          }
        />

        <Route path={`chatbot`} element={<Chatbot />} />
        <Route path="*" element={<Error404 />} />
      </Route>

      <Route
        path="*"
        element={
          <Navigate
            to={`${import.meta.env.BASE_URL}dashboards-ecommerce`}
            replace
          />
        }
      />
    </Routes>
  );
};
