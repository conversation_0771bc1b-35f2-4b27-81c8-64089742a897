import React, { useState, useEffect } from "react";
import { Card, Row, Col, Button, Badge, Form, Modal } from "react-bootstrap";
import { useParams, useNavigate } from "react-router-dom";
import { toast } from "react-hot-toast";
import Pageheader from "../../../../components/pageheader/pageheader";
import {
  useLazyGetSalonConstructionServiceByIdQuery,
  useUpdateSalonConstructionServiceStatusMutation,
  salonConstructionServiceHelpers,
} from "../../../../services/marketing/salon-construction-service";
import {
  ISalonConstructionServiceSignup,
  ESalonConstructionServiceStatus,
  STATUS_LABELS,
  STATUS_COLORS,
  BUDGET_RANGE_LABELS,
  SERVICE_INTEREST_LABELS,
} from "../../../../types/marketing/salon-construction-service";

const SalonConstructionServiceDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const [showRejectModal, setShowRejectModal] = useState(false);
  const [rejectionReason, setRejectionReason] = useState("");

  // RTK Query hooks
  const [triggerGetSignup, { data: signup, isLoading: loading }] =
    useLazyGetSalonConstructionServiceByIdQuery();
  const [updateStatus] = useUpdateSalonConstructionServiceStatusMutation();

  // Fetch signup details
  const fetchSignupDetails = async () => {
    if (!id) return;

    try {
      await triggerGetSignup(id);
    } catch (error) {
      console.error("Error fetching signup details:", error);
      toast.error("Failed to fetch signup details");
      navigate(
        `${import.meta.env.BASE_URL}managements-salon-construction-service`
      );
    }
  };

  useEffect(() => {
    fetchSignupDetails();
  }, [id]);

  // Handle status update
  const handleStatusUpdate = async (
    status: ESalonConstructionServiceStatus,
    rejectionReason?: string
  ) => {
    if (!signup) return;

    try {
      if (status === ESalonConstructionServiceStatus.APPROVED) {
        await updateStatus(
          salonConstructionServiceHelpers.approveSignup(signup.id)
        );
        toast.success("Signup approved successfully");
      } else if (status === ESalonConstructionServiceStatus.REJECTED) {
        await updateStatus(
          salonConstructionServiceHelpers.rejectSignup(
            signup.id,
            rejectionReason
          )
        );
        toast.success("Signup rejected successfully");
      }

      // Refresh data
      fetchSignupDetails();
      setShowRejectModal(false);
      setRejectionReason("");
    } catch (error) {
      console.error("Error updating status:", error);
      toast.error("Failed to update signup status");
    }
  };

  // Handle PDF download
  const handleDownloadPDF = () => {
    if (!signup) return;

    try {
      salonConstructionServiceHelpers.downloadPDF(signup.id);
    } catch (error) {
      console.error("Error downloading PDF:", error);
      toast.error("Failed to download PDF");
    }
  };

  // Handle reject with reason
  const handleRejectWithReason = () => {
    handleStatusUpdate(
      ESalonConstructionServiceStatus.REJECTED,
      rejectionReason
    );
  };

  // Render status badge
  const renderStatusBadge = (status: ESalonConstructionServiceStatus) => {
    return (
      <Badge bg={STATUS_COLORS[status]} className="fs-6">
        {STATUS_LABELS[status]}
      </Badge>
    );
  };

  // Render service interests
  const renderServiceInterests = (interests: string[]) => {
    return (
      <ul className="list-unstyled">
        {interests.map((interest, index) => (
          <li key={index}>
            <i className="fe fe-check text-success me-2"></i>
            {
              SERVICE_INTEREST_LABELS[
                interest as keyof typeof SERVICE_INTEREST_LABELS
              ]
            }
          </li>
        ))}
      </ul>
    );
  };

  if (loading) {
    return (
      <div
        className="d-flex justify-content-center align-items-center"
        style={{ height: "400px" }}
      >
        <div className="spinner-border" role="status">
          <span className="visually-hidden">Loading...</span>
        </div>
      </div>
    );
  }

  if (!signup) {
    return (
      <div className="text-center">
        <h4>Signup not found</h4>
        <Button
          variant="primary"
          onClick={() =>
            navigate(
              `${
                import.meta.env.BASE_URL
              }managements-salon-construction-service`
            )
          }
        >
          Back to List
        </Button>
      </div>
    );
  }

  return (
    <>
      <Pageheader
        title="Salon Construction Service Details"
        heading="Marketing"
        active="Salon Construction Service Details"
      />

      <Row>
        <Col xl={12}>
          <Card>
            <Card.Header className="d-flex justify-content-between align-items-center">
              <h4 className="card-title mb-0">Signup Details</h4>
              <div>
                <Button
                  variant="outline-secondary"
                  className="me-2"
                  onClick={() =>
                    navigate(
                      `${
                        import.meta.env.BASE_URL
                      }managements-salon-construction-service`
                    )
                  }
                >
                  Back to List
                </Button>
                {signup.pdfFile && (
                  <Button
                    variant="outline-primary"
                    className="me-2"
                    onClick={handleDownloadPDF}
                  >
                    <i className="fe fe-download me-1"></i>
                    Download PDF
                  </Button>
                )}
              </div>
            </Card.Header>
            <Card.Body>
              <Row>
                {/* Basic Information */}
                <Col md={6}>
                  <Card className="mb-4">
                    <Card.Header>
                      <h5 className="mb-0">Basic Information</h5>
                    </Card.Header>
                    <Card.Body>
                      <div className="mb-3">
                        <label className="form-label fw-bold">Status</label>
                        <div>{renderStatusBadge(signup.status)}</div>
                      </div>
                      <div className="mb-3">
                        <label className="form-label fw-bold">Full Name</label>
                        <p className="mb-0">{signup.fullName}</p>
                      </div>
                      <div className="mb-3">
                        <label className="form-label fw-bold">
                          Business Name
                        </label>
                        <p className="mb-0">{signup.businessName}</p>
                      </div>
                      <div className="mb-3">
                        <label className="form-label fw-bold">
                          Email Address
                        </label>
                        <p className="mb-0">
                          <a href={`mailto:${signup.emailAddress}`}>
                            {signup.emailAddress}
                          </a>
                        </p>
                      </div>
                      <div className="mb-3">
                        <label className="form-label fw-bold">
                          Phone Number
                        </label>
                        <p className="mb-0">
                          <a href={`tel:${signup.phoneNumber}`}>
                            {signup.phoneNumber}
                          </a>
                        </p>
                      </div>
                      <div className="mb-3">
                        <label className="form-label fw-bold">
                          Salon Address
                        </label>
                        <p className="mb-0">{signup.salonAddress}</p>
                      </div>
                    </Card.Body>
                  </Card>
                </Col>

                {/* Project Details */}
                <Col md={6}>
                  <Card className="mb-4">
                    <Card.Header>
                      <h5 className="mb-0">Project Details</h5>
                    </Card.Header>
                    <Card.Body>
                      <div className="mb-3">
                        <label className="form-label fw-bold">
                          Service Interest
                        </label>
                        {renderServiceInterests(signup.serviceInterest)}
                      </div>
                      <div className="mb-3">
                        <label className="form-label fw-bold">
                          Budget Range
                        </label>
                        <p className="mb-0">
                          {BUDGET_RANGE_LABELS[signup.budgetRange]}
                        </p>
                      </div>
                      {signup.preferredStartDate && (
                        <div className="mb-3">
                          <label className="form-label fw-bold">
                            Preferred Start Date
                          </label>
                          <p className="mb-0">
                            {new Date(
                              signup.preferredStartDate
                            ).toLocaleDateString()}
                          </p>
                        </div>
                      )}
                      {signup.additionalNotes && (
                        <div className="mb-3">
                          <label className="form-label fw-bold">
                            Additional Notes
                          </label>
                          <p className="mb-0">{signup.additionalNotes}</p>
                        </div>
                      )}
                    </Card.Body>
                  </Card>
                </Col>

                {/* Signature & Consent */}
                <Col md={6}>
                  <Card className="mb-4">
                    <Card.Header>
                      <h5 className="mb-0">Signature & Consent</h5>
                    </Card.Header>
                    <Card.Body>
                      <div className="mb-3">
                        <label className="form-label fw-bold">
                          Consent Confirmed
                        </label>
                        <p className="mb-0">
                          <Badge
                            bg={signup.consentConfirmed ? "success" : "danger"}
                          >
                            {signup.consentConfirmed ? "Yes" : "No"}
                          </Badge>
                        </p>
                      </div>
                      {signup.signature && (
                        <div className="mb-3">
                          <label className="form-label fw-bold">
                            Signature
                          </label>
                          <p className="mb-0">{signup.signature}</p>
                        </div>
                      )}
                      {signup.signatureDate && (
                        <div className="mb-3">
                          <label className="form-label fw-bold">
                            Signature Date
                          </label>
                          <p className="mb-0">
                            {new Date(
                              signup.signatureDate
                            ).toLocaleDateString()}
                          </p>
                        </div>
                      )}
                    </Card.Body>
                  </Card>
                </Col>

                {/* System Information */}
                <Col md={6}>
                  <Card className="mb-4">
                    <Card.Header>
                      <h5 className="mb-0">System Information</h5>
                    </Card.Header>
                    <Card.Body>
                      <div className="mb-3">
                        <label className="form-label fw-bold">Created At</label>
                        <p className="mb-0">
                          {new Date(signup.createdAt).toLocaleString()}
                        </p>
                      </div>
                      <div className="mb-3">
                        <label className="form-label fw-bold">Updated At</label>
                        <p className="mb-0">
                          {new Date(signup.updatedAt).toLocaleString()}
                        </p>
                      </div>
                      {signup.pdfFile && (
                        <div className="mb-3">
                          <label className="form-label fw-bold">PDF File</label>
                          <p className="mb-0">
                            <Button
                              variant="link"
                              className="p-0"
                              onClick={handleDownloadPDF}
                            >
                              {signup.pdfFile.fileKey}
                            </Button>
                          </p>
                        </div>
                      )}
                    </Card.Body>
                  </Card>
                </Col>
              </Row>

              {/* Action Buttons */}
              {signup.status === ESalonConstructionServiceStatus.PENDING && (
                <Row>
                  <Col md={12}>
                    <Card>
                      <Card.Header>
                        <h5 className="mb-0">Actions</h5>
                      </Card.Header>
                      <Card.Body>
                        <div className="d-flex gap-2">
                          <Button
                            variant="success"
                            onClick={() =>
                              handleStatusUpdate(
                                ESalonConstructionServiceStatus.APPROVED
                              )
                            }
                          >
                            <i className="fe fe-check me-1"></i>
                            Approve Signup
                          </Button>
                          <Button
                            variant="danger"
                            onClick={() => setShowRejectModal(true)}
                          >
                            <i className="fe fe-x me-1"></i>
                            Reject Signup
                          </Button>
                        </div>
                      </Card.Body>
                    </Card>
                  </Col>
                </Row>
              )}
            </Card.Body>
          </Card>
        </Col>
      </Row>

      {/* Reject Modal */}
      <Modal show={showRejectModal} onHide={() => setShowRejectModal(false)}>
        <Modal.Header closeButton>
          <Modal.Title>Reject Signup</Modal.Title>
        </Modal.Header>
        <Modal.Body>
          <Form>
            <Form.Group>
              <Form.Label>Rejection Reason (Optional)</Form.Label>
              <Form.Control
                as="textarea"
                rows={3}
                value={rejectionReason}
                onChange={(e) => setRejectionReason(e.target.value)}
                placeholder="Enter reason for rejection..."
              />
            </Form.Group>
          </Form>
        </Modal.Body>
        <Modal.Footer>
          <Button variant="secondary" onClick={() => setShowRejectModal(false)}>
            Cancel
          </Button>
          <Button variant="danger" onClick={handleRejectWithReason}>
            Reject Signup
          </Button>
        </Modal.Footer>
      </Modal>
    </>
  );
};

export default SalonConstructionServiceDetails;
