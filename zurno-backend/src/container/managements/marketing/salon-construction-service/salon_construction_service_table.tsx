import React, { useState, useEffect } from "react";
import { Card, Row, Col, Form, Button, Badge, Dropdown } from "react-bootstrap";
import { <PERSON> } from "react-router-dom";
import { toast } from "react-hot-toast";
import Pageheader from "../../../../components/pageheader/pageheader";
import TableTitle from "../../../../components/table-title/table-title";
import PaginationBar from "../../../../components/pagination-bar/pagination-bar";
import SearchBar from "../../../../components/search-bar/search-bar";
import salonConstructionServiceAPI from "../../../../services/marketing/salon-construction-service";
import {
  ISalonConstructionServiceSignup,
  ISalonConstructionServiceFilters,
  ESalonConstructionServiceStatus,
  EBudgetRange,
  STATUS_LABELS,
  STATUS_COLORS,
  BUDGET_RANGE_LABELS,
  SERVICE_INTEREST_LABELS,
} from "../../../../types/marketing/salon-construction-service";

const SalonConstructionServiceTable: React.FC = () => {
  const [signups, setSignups] = useState<ISalonConstructionServiceSignup[]>([]);
  const [loading, setLoading] = useState(false);
  const [totalItems, setTotalItems] = useState(0);
  const [statistics, setStatistics] = useState({
    total: 0,
    pending: 0,
    approved: 0,
    rejected: 0,
  });

  const [filters, setFilters] = useState<ISalonConstructionServiceFilters>({
    status: "all",
    search: "",
    startDate: "",
    endDate: "",
    budgetRange: "all",
    page: 1,
    limit: 10,
  });

  // Fetch signups data
  const fetchSignups = async () => {
    setLoading(true);
    try {
      const params = {
        page: filters.page,
        limit: filters.limit,
        ...(filters.status !== "all" && {
          status: filters.status as ESalonConstructionServiceStatus,
        }),
        ...(filters.search && { search: filters.search }),
        ...(filters.startDate && { startDate: filters.startDate }),
        ...(filters.endDate && { endDate: filters.endDate }),
        ...(filters.budgetRange !== "all" && {
          budgetRange: filters.budgetRange as EBudgetRange,
        }),
      };

      const response = await salonConstructionServiceAPI.getSignups(params);
      setSignups(response.data.data);
      setTotalItems(response.data.meta.total);
    } catch (error) {
      console.error("Error fetching signups:", error);
      toast.error("Failed to fetch salon construction service signups");
    } finally {
      setLoading(false);
    }
  };

  // Fetch statistics
  const fetchStatistics = async () => {
    try {
      const response = await salonConstructionServiceAPI.getStatistics();
      setStatistics(response.data);
    } catch (error) {
      console.error("Error fetching statistics:", error);
    }
  };

  useEffect(() => {
    fetchSignups();
  }, [filters]);

  useEffect(() => {
    fetchStatistics();
  }, []);

  // Handle filter changes
  const handleFilterChange = (
    key: keyof ISalonConstructionServiceFilters,
    value: any
  ) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
      page: key !== "page" ? 1 : value, // Reset to page 1 when other filters change
    }));
  };

  // Handle search
  const handleSearch = (searchTerm: string) => {
    handleFilterChange("search", searchTerm);
  };

  // Handle pagination
  const handlePageChange = (page: number) => {
    handleFilterChange("page", page);
  };

  // Handle status update
  const handleStatusUpdate = async (
    id: string,
    status: ESalonConstructionServiceStatus,
    rejectionReason?: string
  ) => {
    try {
      if (status === ESalonConstructionServiceStatus.APPROVED) {
        await salonConstructionServiceAPI.approveSignup(id);
        toast.success("Signup approved successfully");
      } else if (status === ESalonConstructionServiceStatus.REJECTED) {
        await salonConstructionServiceAPI.rejectSignup(id, rejectionReason);
        toast.success("Signup rejected successfully");
      }

      // Refresh data
      fetchSignups();
      fetchStatistics();
    } catch (error) {
      console.error("Error updating status:", error);
      toast.error("Failed to update signup status");
    }
  };

  // Handle PDF download
  const handleDownloadPDF = (id: string) => {
    try {
      salonConstructionServiceAPI.downloadPDF(id);
    } catch (error) {
      console.error("Error downloading PDF:", error);
      toast.error("Failed to download PDF");
    }
  };

  // Render status badge
  const renderStatusBadge = (status: ESalonConstructionServiceStatus) => {
    return <Badge bg={STATUS_COLORS[status]}>{STATUS_LABELS[status]}</Badge>;
  };

  // Render service interests
  const renderServiceInterests = (interests: string[]) => {
    return interests
      .map(
        (interest) =>
          SERVICE_INTEREST_LABELS[
            interest as keyof typeof SERVICE_INTEREST_LABELS
          ]
      )
      .join(", ");
  };

  // Render action buttons
  const renderActions = (signup: ISalonConstructionServiceSignup) => {
    return (
      <Dropdown>
        <Dropdown.Toggle variant="outline-primary" size="sm">
          Actions
        </Dropdown.Toggle>
        <Dropdown.Menu>
          <Dropdown.Item
            as={Link}
            to={`${
              import.meta.env.BASE_URL
            }managements-salon-construction-service/details/${signup.id}`}
          >
            View Details
          </Dropdown.Item>
          {signup.pdfFile && (
            <Dropdown.Item onClick={() => handleDownloadPDF(signup.id)}>
              Download PDF
            </Dropdown.Item>
          )}
          {signup.status === ESalonConstructionServiceStatus.PENDING && (
            <>
              <Dropdown.Divider />
              <Dropdown.Item
                onClick={() =>
                  handleStatusUpdate(
                    signup.id,
                    ESalonConstructionServiceStatus.APPROVED
                  )
                }
                className="text-success"
              >
                Approve
              </Dropdown.Item>
              <Dropdown.Item
                onClick={() => {
                  const reason = prompt("Enter rejection reason (optional):");
                  handleStatusUpdate(
                    signup.id,
                    ESalonConstructionServiceStatus.REJECTED,
                    reason || undefined
                  );
                }}
                className="text-danger"
              >
                Reject
              </Dropdown.Item>
            </>
          )}
        </Dropdown.Menu>
      </Dropdown>
    );
  };

  return (
    <>
      <Pageheader
        title="Salon Construction Service"
        heading="Marketing"
        active="Salon Construction Service"
      />

      {/* Statistics Cards */}
      <Row className="mb-4">
        <Col md={3}>
          <Card className="text-center">
            <Card.Body>
              <h3 className="text-primary">{statistics.total}</h3>
              <p className="mb-0">Total Signups</p>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="text-center">
            <Card.Body>
              <h3 className="text-warning">{statistics.pending}</h3>
              <p className="mb-0">Pending</p>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="text-center">
            <Card.Body>
              <h3 className="text-success">{statistics.approved}</h3>
              <p className="mb-0">Approved</p>
            </Card.Body>
          </Card>
        </Col>
        <Col md={3}>
          <Card className="text-center">
            <Card.Body>
              <h3 className="text-danger">{statistics.rejected}</h3>
              <p className="mb-0">Rejected</p>
            </Card.Body>
          </Card>
        </Col>
      </Row>

      <Row>
        <Col xl={12}>
          <Card>
            <Card.Header>
              <TableTitle title="Salon Construction Service Signups" />
            </Card.Header>
            <Card.Body>
              {/* Filters */}
              <Row className="mb-3">
                <Col md={3}>
                  <SearchBar
                    onSearch={handleSearch}
                    placeholder="Search by name, email, or business..."
                  />
                </Col>
                <Col md={2}>
                  <Form.Select
                    value={filters.status}
                    onChange={(e) =>
                      handleFilterChange("status", e.target.value)
                    }
                  >
                    <option value="all">All Status</option>
                    <option value={ESalonConstructionServiceStatus.PENDING}>
                      Pending
                    </option>
                    <option value={ESalonConstructionServiceStatus.APPROVED}>
                      Approved
                    </option>
                    <option value={ESalonConstructionServiceStatus.REJECTED}>
                      Rejected
                    </option>
                  </Form.Select>
                </Col>
                <Col md={2}>
                  <Form.Select
                    value={filters.budgetRange}
                    onChange={(e) =>
                      handleFilterChange("budgetRange", e.target.value)
                    }
                  >
                    <option value="all">All Budgets</option>
                    {Object.entries(BUDGET_RANGE_LABELS).map(([key, label]) => (
                      <option key={key} value={key}>
                        {label}
                      </option>
                    ))}
                  </Form.Select>
                </Col>
                <Col md={2}>
                  <Form.Control
                    type="date"
                    value={filters.startDate}
                    onChange={(e) =>
                      handleFilterChange("startDate", e.target.value)
                    }
                    placeholder="Start Date"
                  />
                </Col>
                <Col md={2}>
                  <Form.Control
                    type="date"
                    value={filters.endDate}
                    onChange={(e) =>
                      handleFilterChange("endDate", e.target.value)
                    }
                    placeholder="End Date"
                  />
                </Col>
                <Col md={1}>
                  <Button
                    variant="outline-secondary"
                    onClick={() =>
                      setFilters({
                        status: "all",
                        search: "",
                        startDate: "",
                        endDate: "",
                        budgetRange: "all",
                        page: 1,
                        limit: 10,
                      })
                    }
                  >
                    Clear
                  </Button>
                </Col>
              </Row>

              {/* Table */}
              <div className="table-responsive">
                <table className="table table-bordered text-nowrap border-bottom">
                  <thead>
                    <tr>
                      <th>Business Name</th>
                      <th>Owner Name</th>
                      <th>Email</th>
                      <th>Phone</th>
                      <th>Service Interest</th>
                      <th>Budget Range</th>
                      <th>Status</th>
                      <th>Created Date</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {loading ? (
                      <tr>
                        <td colSpan={9} className="text-center">
                          Loading...
                        </td>
                      </tr>
                    ) : signups.length === 0 ? (
                      <tr>
                        <td colSpan={9} className="text-center">
                          No signups found
                        </td>
                      </tr>
                    ) : (
                      signups.map((signup) => (
                        <tr key={signup.id}>
                          <td>{signup.businessName}</td>
                          <td>{signup.fullName}</td>
                          <td>{signup.emailAddress}</td>
                          <td>{signup.phoneNumber}</td>
                          <td
                            className="text-truncate"
                            style={{ maxWidth: "200px" }}
                          >
                            {renderServiceInterests(signup.serviceInterest)}
                          </td>
                          <td>{BUDGET_RANGE_LABELS[signup.budgetRange]}</td>
                          <td>{renderStatusBadge(signup.status)}</td>
                          <td>
                            {new Date(signup.createdAt).toLocaleDateString()}
                          </td>
                          <td>{renderActions(signup)}</td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              <PaginationBar
                currentPage={filters.page}
                totalItems={totalItems}
                itemsPerPage={filters.limit}
                onPageChange={handlePageChange}
              />
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </>
  );
};

export default SalonConstructionServiceTable;
