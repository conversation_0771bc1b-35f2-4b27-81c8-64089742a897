import { FC, Fragment, useEffect, useState } from "react";
import {
  <PERSON>ton, Card, Col,
  Form,
  InputGroup,
  OverlayTrigger,
  Row,
  Table,
  Tooltip
} from "react-bootstrap";
import { useNavigate } from "react-router-dom";
import LazySelect from "../../../../components/lazy-select/lazy-select";
import { LoadingOverlay } from "../../../../components/loading/loading-overlay";
import PaginationBar from "../../../../components/pagination-bar/pagination-bar";
import deleteSweetAlert from "../../../../components/sweet-alerts/delete_swal";
import CardHeaderWithBack from "../../../../components/table-title/card-header-with-back";
import { useLazySelectCityQuery, useLazySelectCountryQuery, useLazySelectStateQuery } from "../../../../services/locations";
import {
  useLazyExportStoreReportQuery,
  useLazyDeleteStoreByIdQuery,
  useLazyListStoreQuery,
} from "../../../../services/store/store";
import { useLazySelectUserQuery } from "../../../../services/user";
import { hasPermission } from "../../../../utils/authorization";
import { ACTION, RESOURCE } from "../../../../utils/constant/authorization";
import axios, { AxiosRequestConfig } from "axios";
interface ManagementStoreProps { }

const ManagementStore: FC<ManagementStoreProps> = () => {
  const [page, setPage] = useState(1);
  // const [limit] = useState(10);
  const [lastPage, setLastPage] = useState(20);
  const [total, setTotal] = useState(20);
  const [search, setSearch] = useState<string>("");

  const [isLoading, setIsLoading] = useState(false);
  // const [isInitial, setIsInitial] = useState(true);
  const [err, setErr] = useState<any[] | any>();

  const [trigger] = useLazyListStoreQuery();
  const [deleteById] = useLazyDeleteStoreByIdQuery();

  let data: TReponsePaging<TStore> | undefined = [];

  const [stores, setStores] = useState(data);
  const navigate = useNavigate()

  const [selectCountry] = useLazySelectCountryQuery()
  const [selectState] = useLazySelectStateQuery()
  const [selectCity] = useLazySelectCityQuery()
  const [location, setLocation] = useState({
    countryIds: [],
    stateIds: [],
    cityIds: [],
  })

  const [selectOwner] = useLazySelectUserQuery()
  const [ownerId, setOwnerId] = useState('')

  const [verifiedStatus, setVerifiedStatus] = useState('')

  const loadData = (input: {
    page: number,
    search?: string,
    ownerIds?: string[],
    verified?: string | null,
  } & Partial<typeof location>
  ) => {
    setIsLoading(true);
    trigger(input)
      .then((res) => {
        console.log(res);
        setStores(res.data?.data || []);
        setLastPage(res?.data?.meta?.lastPage);
        setTotal(res?.data?.meta?.total);
      }).finally(() => {
        // setIsInitial(false);
        setIsLoading(false);
      });
  };

  const [startSearch, setStartSearch] = useState(false)
  const [searchMode, setSearchMode] = useState(false)
  useEffect(() => {
    if (searchMode) {
      loadData({ page, search, ownerIds: [ownerId], ...location, verified: verifiedStatus })
    } else {
      loadData({ page });
    }
  }, [page, startSearch]);

  const handleMessageAreaKeyDown = (event) => {
    if (event.keyCode == 13 && event.shiftKey == false) {
      event.preventDefault()
      handleStartSearch()
    }
  }

  const handleStartSearch = () => {
    setPage(1)
    setStartSearch(!startSearch)
    setSearchMode(true)
  }

  const [reset, setReset] = useState(false)
  const [resetCountry, setResetCountry] = useState(false)
  const [resetState, setResetState] = useState(false)
  const [resetCity, setResetCity] = useState(false)
  const handleStopSearch = () => {
    setPage(1)
    setSearch('')
    setReset(true)
    setResetCountry(true)
    setResetState(true)
    setResetCity(true)
    setSearchMode(false)
    setOwnerId('')
    setVerifiedStatus('')
    setStartSearch(!startSearch)
  }

  const handleDeleteClick = (postId: string, name: string) => {
    deleteSweetAlert({
      id: postId,
      deleteAction: deleteById,
      confirmText: `Do you want to delete ${name}?`,
      finishAction: () => loadData({ page, search, ...location }),
    });
  };

  const [forceSearchState, setForceSearchState] = useState(false)
  const [forceSearchCity, setForceSearchCity] = useState(false)
  const handleLocationSelected = (value: any, location: 'countries' | 'states' | 'cities') => {
    switch (location) {
      case ('countries'): {
        setLocation((prev) => ({ ...prev, countryIds: value?.map(val => val.id) }))
        setResetState(true)
        setResetCity(true)
        setForceSearchState(true)
        setForceSearchCity(true)
        break
      }
      case ('states'): {
        setLocation((prev) => ({ ...prev, stateIds: value?.map(val => val.id) }))
        setResetCity(true)
        setForceSearchCity(true)
        break
      }
      case ('cities'): {
        setLocation((prev) => ({ ...prev, cityIds: value?.map(val => val.id) }))
        break
      }
      default: { break }
    }
  }

  const [exportStoreReport] = useLazyExportStoreReportQuery()
  const handleExportClick = (event) => {
    if (event) { event.preventDefault() }

    setIsLoading(true)
    exportStoreReport({ page, search, ownerIds: [ownerId], ...location })
      .then((res) => {
        if (res.data) {
          const headers = { 'Content-Type': 'blob' };
          const config: AxiosRequestConfig = {
            method: 'GET',
            url: res.data.url,
            responseType: 'arraybuffer',
            headers
          };
          axios(config)
            .then((response) => {
              const url = URL.createObjectURL(new Blob([response.data]));
              const link = document.createElement('a');
              link.href = url;
              link.download = res.data?.filename || ""
              link.click();
            })
            .catch((error) => console.log(error))
        }
      })
      .catch((error) => console.log(error))
      .finally(() => setIsLoading(false))
  }

  return (
    <Fragment>
      {isLoading && <LoadingOverlay />}
      <Row>
        <Col xl={12}>
          <Card className="custom-card">
            <Card.Header>
              <CardHeaderWithBack
                title="Store Management"
                route=""
              ></CardHeaderWithBack>
              <Card.Subtitle>
                <Button
                  variant="success-light"
                  className="mx-2"
                  onClick={handleExportClick}
                >
                  Export<i className="bi bi-download ms-2" />
                </Button>
                <Button
                  hidden={!hasPermission(ACTION.CREATE, RESOURCE.STORE)}
                  variant="primary-light"
                  type="submit"
                  onClick={() => navigate("/managements-store/create/")}
                >
                  Add<i className="bi bi-plus-lg ms-2"></i>
                </Button>
              </Card.Subtitle>
            </Card.Header>
            <Card.Body className="overflow-auto">
              <div className="app-container">
                <InputGroup>
                  <Form.Control
                    type="search"
                    className="mb-3"
                    value={search}
                    onChange={(event) => setSearch(event.target.value)}
                    placeholder="Type a keyword..."
                    onKeyDown={handleMessageAreaKeyDown}
                  />
                </InputGroup>
                <div className="mb-3">
                  <Row className="mb-3">
                    <Col lg={4}>
                      <InputGroup>
                        <InputGroup.Text>Country</InputGroup.Text>
                        <div style={{ width: '-webkit-fill-available' }}>
                          <LazySelect
                            isMulti
                            selectionFunction={selectCountry}
                            label={value => value.name}
                            getSelectedOptions={(value) => handleLocationSelected(value, 'countries')}
                            clearSelected={[resetCountry, setResetCountry]}
                            isClearable
                          />
                        </div>
                      </InputGroup>
                    </Col>
                    <Col lg={4}>
                      <InputGroup>
                        <InputGroup.Text>State</InputGroup.Text>
                        <div style={{ width: '-webkit-fill-available' }}>
                          <LazySelect
                            isMulti
                            selectionFunction={selectState}
                            selectionFunctionQuery={{ countryIds: location.countryIds }}
                            label={value => value.name}
                            getSelectedOptions={(value) => handleLocationSelected(value, 'states')}
                            clearSelected={[resetState, setResetState]}
                            forceSearch={[forceSearchState, setForceSearchState]}
                            isClearable
                          />
                        </div>
                      </InputGroup>
                    </Col>
                    <Col lg={4}>
                      <InputGroup>
                        <InputGroup.Text>City</InputGroup.Text>
                        <div style={{ width: '-webkit-fill-available' }}>
                          <LazySelect
                            isMulti
                            selectionFunction={selectCity}
                            selectionFunctionQuery={{ stateIds: location.stateIds }}
                            label={value => value.name}
                            getSelectedOptions={(value) => handleLocationSelected(value, 'cities')}
                            clearSelected={[resetCity, setResetCity]}
                            forceSearch={[forceSearchCity, setForceSearchCity]}
                            isClearable
                          />
                        </div>
                      </InputGroup>
                    </Col>
                  </Row>
                  <Row>
                    <Col lg={4}>
                      <InputGroup>
                        <InputGroup.Text>Owner</InputGroup.Text>
                        <div style={{ width: '-webkit-fill-available' }}>
                          <LazySelect
                            selectionFunction={selectOwner}
                            label={value => value.email}
                            getSelectedOptions={value => setOwnerId(value?.id)}
                            clearSelected={[reset, setReset]}
                            isClearable
                          />
                        </div>
                      </InputGroup>
                    </Col>
                    <Col lg={4}>
                      <InputGroup>
                        <InputGroup.Text>Status</InputGroup.Text>
                        <Form.Select
                          value={verifiedStatus}
                          onChange={(e) => setVerifiedStatus(e.target.value)}
                        >
                          <option value=''>All</option>
                          <option value={1}>Verified</option>
                          <option value={0}>Unverified</option>
                        </Form.Select>
                      </InputGroup>
                    </Col>
                    <Col lg={4} className="text-end">
                      <Button
                        className="mx-1"
                        onClick={handleStartSearch}
                      >
                        Search
                      </Button>
                      <Button onClick={handleStopSearch}>
                        Reset
                      </Button>
                    </Col>
                  </Row>
                </div>
                <Table
                  id="delete-datatable"
                  className="table table-bordered text-nowrap border-bottom"
                >
                  <thead>
                    <tr>
                      <th>Logo</th>
                      <th>Name</th>
                      <th>Address</th>
                      <th>Phone Number</th>
                      <th>Status</th>
                      <th>Owner</th>
                      <th>Action</th>
                    </tr>
                  </thead>
                  <tbody>
                    {stores.map((store: any) => (
                      <Fragment key={store.id}>
                        <ReadOnlyRow
                          store={store}
                          err={err}
                          setErr={setErr}
                          handleDeleteClick={handleDeleteClick}
                        />
                      </Fragment>
                    ))}
                  </tbody>
                </Table>
                <PaginationBar
                  page={page}
                  setPage={setPage}
                  lastPage={lastPage}
                  total={total}
                />
              </div>
            </Card.Body>
          </Card>
        </Col>
      </Row>
    </Fragment >
  );
};


const ReadOnlyRow = ({
  store,
  handleDeleteClick
}: any) => {
  const navigate = useNavigate();

  return (
    <tr>
      <td
        style={{
          textAlign: "center",
          padding: "10px",
          width: "100px",
          height: "100px",
          overflow: "hidden",
        }}
      >
        <p className="avatar avatar-xxl my-auto">
          {
            store.logo
            && <img
              src={store.logo?.url || ""}
              style={{
                display: "block",
                margin: "0 auto",
                width: "100%",
                height: "100%",
                objectFit: "cover",
              }}
            />
          }
        </p>
      </td>
      <td
        style={{
          maxWidth: "100px",
          whiteSpace: "nowrap",
          overflow: "hidden",
          textOverflow: "ellipsis",
        }}
      >
        <span
          onClick={() => navigate(`/managements-store/edit/${store.id}`)}
          style={{ cursor: "pointer" }}
        >
          {store.name}
        </span>
      </td>
      <td
        style={{
          maxWidth: "150px",
          whiteSpace: "nowrap",
          overflow: "hidden",
          textOverflow: "ellipsis",
        }}

      >
        {store.address}
      </td>
      <td>{store.phoneNumber}</td>
      <td>
        {store.verified === 1 ? (
          <span className="badge bg-success-transparent rounded-pill">
            Verified
          </span>
        ) : (
          <span className="badge bg-danger-transparent rounded-pill">
            Unverified
          </span>
        )}
      </td>
      <td>
        {store.user?.firstName} {store.user?.lastName}
      </td>

      <td>
        <OverlayTrigger placement="top" overlay={<Tooltip>Update</Tooltip>}>
          <Button
            hidden={!hasPermission(ACTION.UPDATE, RESOURCE.STORE)}
            variant="primary-light"
            className="btn btn-warning-light btn-sm ms-2"
            onClick={() => navigate(`/managements-store/edit/${store.id}`)}
          >
            <span className="ri-edit-line fs-14"></span>
          </Button>
        </OverlayTrigger>

        <OverlayTrigger placement="top" overlay={<Tooltip>Delete</Tooltip>}>
          <Button
            hidden={!hasPermission(ACTION.DELETE, RESOURCE.STORE)}
            variant="primary-light"
            className="btn btn-danger-light btn-sm ms-2"
            onClick={() => handleDeleteClick(store.id, store.name)}
          >
            <span className="ri-delete-bin-7-line fs-14"></span>
          </Button>
        </OverlayTrigger>
      </td>
    </tr>
  );
};
export default ManagementStore;
