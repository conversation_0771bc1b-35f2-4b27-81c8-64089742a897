import { apiService } from "../api";

const ENDPOINT = "v1/admin/salon-construction-service";

export const salonConstructionServiceService = apiService.injectEndpoints({
  endpoints: (build) => ({
    listSalonConstructionService: build.query<
      TReponsePaging<ISalonConstructionServiceSignup>,
      TQueryAPI
    >({
      query: (params) => {
        return {
          url: ENDPOINT,
          method: "GET",
          params,
        };
      },
      transformResponse: (
        rawResult: TReponsePaging<ISalonConstructionServiceSignup>
      ) => {
        const data = rawResult.data?.map(
          (item: ISalonConstructionServiceSignup, index) => ({
            ...item,
            _rowIndex: index + 1,
          })
        );
        return { ...rawResult, data: data || [] };
      },
    }),
    getSalonConstructionServiceById: build.query<
      ISalonConstructionServiceSignup,
      string
    >({
      query: (id: string) => {
        return {
          url: `${ENDPOINT}/${id}`,
          method: "GET",
        };
      },
      transformResponse: (rawResult: ISalonConstructionServiceSignup) => {
        return rawResult;
      },
    }),
    updateSalonConstructionServiceStatus: build.mutation<
      ISalonConstructionServiceSignup,
      { id: string } & ISalonConstructionServiceUpdateRequest
    >({
      query: ({ id, ...payload }) => {
        return {
          url: `${ENDPOINT}/${id}`,
          method: "PUT",
          data: payload,
        };
      },
      transformResponse: (rawResult: ISalonConstructionServiceSignup) => {
        return rawResult;
      },
    }),
    getSalonConstructionServiceStats: build.query<
      {
        total: number;
        pending: number;
        approved: number;
        rejected: number;
      },
      void
    >({
      query: () => {
        return {
          url: `${ENDPOINT}/stats`,
          method: "GET",
        };
      },
      transformResponse: (rawResult: {
        success: boolean;
        data: {
          total: number;
          pending: number;
          approved: number;
          rejected: number;
        };
      }) => {
        return rawResult.data;
      },
    }),
  }),
  overrideExisting: true,
});

export const {
  useLazyListSalonConstructionServiceQuery,
  useLazyGetSalonConstructionServiceByIdQuery,
  useUpdateSalonConstructionServiceStatusMutation,
  useLazyGetSalonConstructionServiceStatsQuery,
} = salonConstructionServiceService;

// Helper functions for common operations
export const salonConstructionServiceHelpers = {
  approveSignup: (id: string) => ({
    id,
    status: ESalonConstructionServiceStatus.APPROVED,
  }),
  rejectSignup: (id: string, rejectionReason?: string) => ({
    id,
    status: ESalonConstructionServiceStatus.REJECTED,
    ...(rejectionReason && { rejectionReason }),
  }),
  downloadPDF: (id: string) => {
    const url = `${import.meta.env.VITE_APP_API_URL}/${ENDPOINT}/${id}/pdf`;
    window.open(url, "_blank");
  },
  getPDFUrl: (id: string) => {
    return `${import.meta.env.VITE_APP_API_URL}/${ENDPOINT}/${id}/pdf`;
  },
};
