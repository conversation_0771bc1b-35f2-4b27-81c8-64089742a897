import { AxiosResponse } from "axios";
import { apiService } from "../api";
import {
  ISalonConstructionServiceSignup,
  ISalonConstructionServiceListParams,
  ISalonConstructionServiceListResponse,
  ISalonConstructionServiceResponse,
  ISalonConstructionServiceUpdateRequest,
  ISalonConstructionServiceStatsResponse,
  ESalonConstructionServiceStatus,
} from "../../types/marketing/salon-construction-service";

class SalonConstructionServiceAPI {
  private baseUrl = "/v1/admin/salon-construction-service";

  /**
   * Get all salon construction service signups with filtering and pagination
   */
  async getSignups(
    params: ISalonConstructionServiceListParams = {}
  ): Promise<ISalonConstructionServiceListResponse> {
    try {
      const queryParams = new URLSearchParams();

      if (params.page) queryParams.append("page", params.page.toString());
      if (params.limit) queryParams.append("limit", params.limit.toString());
      if (params.status) queryParams.append("status", params.status);
      if (params.search) queryParams.append("search", params.search);
      if (params.startDate) queryParams.append("startDate", params.startDate);
      if (params.endDate) queryParams.append("endDate", params.endDate);
      if (params.budgetRange)
        queryParams.append("budgetRange", params.budgetRange);

      const response: AxiosResponse<ISalonConstructionServiceListResponse> =
        await apiService.get(`${this.baseUrl}?${queryParams.toString()}`);
      return response.data;
    } catch (error) {
      console.error(
        "Error fetching salon construction service signups:",
        error
      );
      throw error;
    }
  }

  /**
   * Get a specific salon construction service signup by ID
   */
  async getSignup(id: string): Promise<ISalonConstructionServiceResponse> {
    try {
      const response: AxiosResponse<ISalonConstructionServiceResponse> =
        await apiService.get(`${this.baseUrl}/${id}`);
      return response.data;
    } catch (error) {
      console.error("Error fetching salon construction service signup:", error);
      throw error;
    }
  }

  /**
   * Update salon construction service signup status (approve/reject)
   */
  async updateSignupStatus(
    id: string,
    updateData: ISalonConstructionServiceUpdateRequest
  ): Promise<ISalonConstructionServiceResponse> {
    try {
      const response: AxiosResponse<ISalonConstructionServiceResponse> =
        await apiService.put(`${this.baseUrl}/${id}`, updateData);
      return response.data;
    } catch (error) {
      console.error(
        "Error updating salon construction service signup status:",
        error
      );
      throw error;
    }
  }

  /**
   * Approve a salon construction service signup
   */
  async approveSignup(id: string): Promise<ISalonConstructionServiceResponse> {
    return this.updateSignupStatus(id, {
      status: ESalonConstructionServiceStatus.APPROVED,
    });
  }

  /**
   * Reject a salon construction service signup
   */
  async rejectSignup(
    id: string,
    rejectionReason?: string
  ): Promise<ISalonConstructionServiceResponse> {
    return this.updateSignupStatus(id, {
      status: ESalonConstructionServiceStatus.REJECTED,
      rejectionReason,
    });
  }

  /**
   * Get statistics for salon construction service signups
   */
  async getStatistics(): Promise<ISalonConstructionServiceStatsResponse> {
    try {
      const response: AxiosResponse<ISalonConstructionServiceStatsResponse> =
        await apiService.get(`${this.baseUrl}/stats`);
      return response.data;
    } catch (error) {
      console.error(
        "Error fetching salon construction service statistics:",
        error
      );
      throw error;
    }
  }

  /**
   * Download PDF for a specific signup
   */
  downloadPDF(id: string): void {
    try {
      const url = `${this.baseUrl}/${id}/pdf`;
      window.open(url, "_blank");
    } catch (error) {
      console.error("Error downloading PDF:", error);
      throw error;
    }
  }

  /**
   * Get PDF URL for a specific signup
   */
  getPDFUrl(id: string): string {
    return `${this.baseUrl}/${id}/pdf`;
  }

  /**
   * Export signups data (if needed for reporting)
   */
  async exportSignups(
    params: ISalonConstructionServiceListParams = {}
  ): Promise<Blob> {
    try {
      const queryParams = new URLSearchParams();

      if (params.status) queryParams.append("status", params.status);
      if (params.search) queryParams.append("search", params.search);
      if (params.startDate) queryParams.append("startDate", params.startDate);
      if (params.endDate) queryParams.append("endDate", params.endDate);
      if (params.budgetRange)
        queryParams.append("budgetRange", params.budgetRange);

      const response = await api.get(
        `${this.baseUrl}/export?${queryParams.toString()}`,
        { responseType: "blob" }
      );
      return response.data;
    } catch (error) {
      console.error(
        "Error exporting salon construction service signups:",
        error
      );
      throw error;
    }
  }

  /**
   * Get signup counts by status for dashboard widgets
   */
  async getStatusCounts(): Promise<{ [key: string]: number }> {
    try {
      const stats = await this.getStatistics();
      return {
        total: stats.data.total,
        pending: stats.data.pending,
        approved: stats.data.approved,
        rejected: stats.data.rejected,
      };
    } catch (error) {
      console.error("Error fetching status counts:", error);
      throw error;
    }
  }

  /**
   * Search signups by various criteria
   */
  async searchSignups(
    searchTerm: string,
    filters: Partial<ISalonConstructionServiceListParams> = {}
  ): Promise<ISalonConstructionServiceListResponse> {
    return this.getSignups({
      ...filters,
      search: searchTerm,
      page: 1,
      limit: filters.limit || 10,
    });
  }

  /**
   * Get recent signups (last 7 days)
   */
  async getRecentSignups(
    limit: number = 10
  ): Promise<ISalonConstructionServiceListResponse> {
    const endDate = new Date().toISOString().split("T")[0];
    const startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
      .toISOString()
      .split("T")[0];

    return this.getSignups({
      startDate,
      endDate,
      limit,
      page: 1,
    });
  }
}

// Export singleton instance
const salonConstructionServiceAPI = new SalonConstructionServiceAPI();
export default salonConstructionServiceAPI;
