import { ACTION, RESOURCE } from "../../../utils/constant/authorization";

const iconUser = <i className="bx bx-user"></i>;
const icon1 = <i className="bx bx-desktop"></i>;
const manageClassifiedIcon = <i className="bx bx-file"></i>;
const roleIcon = <i className="bx bxs-lock"></i>;
const permissionIcon = <i className="bx bxs-key"></i>;
const mobileSettingIcon = <i className="bx bx-mobile-alt"></i>;
const storeIcon = <i className="bx bx-store-alt"></i>;
const manageProductIcon = <i className="bx bxs-package"></i>;
const vendorIcon = <i className="bi bi-shop"></i>;
const manageWarehouseIcon = <i className="bx bxs-buildings"></i>;
const manageUser = <i className="bx bx-group"></i>;
const rewardPoint = <i className="bx bx-medal"></i>;
const orderIcon = <i className="bx bx-shopping-bag"></i>;
const reportIcon = <i className="bx bxs-report"></i>;
const affiliationIcon = <i className="bx bxs-file"></i>;
const chatbotIcon = <i className="bx bx-chat"></i>;

export const MENUITEMS = [
  {
    menutitle: "MAIN",
    roles: ["super_admin", "admin"],
  },
  {
    title: "Dashboard",
    icon: icon1,
    type: "sub",
    active: false,
    selected: false,
    dirchange: false,
    children: [
      {
        path: `${import.meta.env.BASE_URL}dashboards-ecommerce`,
        type: "link",
        active: false,
        selected: false,
        dirchange: false,
        title: "Ecommerce",
        permissions: [`${ACTION.READ}:${RESOURCE.DASHBOARD}`],
      },
      {
        path: `${import.meta.env.BASE_URL}dashboards/marketplace`,
        type: "link",
        active: false,
        selected: false,
        dirchange: false,
        title: "Marketplace",
        permissions: [`${ACTION.READ}:${RESOURCE.DASHBOARD}`],
      },
      {
        path: `${import.meta.env.BASE_URL}dashboards-analytics`,
        type: "link",
        active: false,
        selected: false,
        dirchange: false,
        title: "Analytics",
        permissions: [`${ACTION.READ}:${RESOURCE.DASHBOARD}`],
      },
    ],
    roles: ["super_admin", "admin"],
    permissions: [ACTION.READ + ":" + RESOURCE.DASHBOARD],
  },
  {
    menutitle: "GENERAL",
    roles: ["super_admin", "admin"],
  },
  {
    title: "Affiliation",
    icon: affiliationIcon,
    type: "sub",
    active: false,
    selected: false,
    dirchange: false,
    children: [
      {
        path: `${import.meta.env.BASE_URL}managements-affiliate-tiers`,
        type: "link",
        active: false,
        selected: false,
        dirchange: false,
        title: "Tiers",
        roles: ["super_admin", "admin"],
        permissions: [`${ACTION.READ}:${RESOURCE.AFFILIATION}`],
      },
      {
        path: `${import.meta.env.BASE_URL}managements-affiliates`,
        type: "link",
        active: false,
        selected: false,
        dirchange: false,
        title: "Affiliates",
        roles: ["super_admin", "admin"],
        permissions: [`${ACTION.READ}:${RESOURCE.AFFILIATION}`],
      },
      {
        path: `${import.meta.env.BASE_URL}managements-affiliate-registration`,
        type: "link",
        active: false,
        selected: false,
        dirchange: false,
        title: "Registration",
        roles: ["super_admin", "admin"],
        permissions: [`${ACTION.READ}:${RESOURCE.AFFILIATION}`],
      },
      {
        path: `${import.meta.env.BASE_URL}commissions`,
        type: "link",
        active: false,
        selected: false,
        dirchange: false,
        title: "Commissions",
        roles: ["super_admin", "admin"],
        permissions: [`${ACTION.READ}:${RESOURCE.AFFILIATION}`],
      },
    ],
  },
  {
    path: `${import.meta.env.BASE_URL}managements-order`,
    icon: orderIcon,
    type: "sub",
    active: false,
    selected: false,
    dirchange: false,
    title: "Orders",
    children: [
      {
        path: `${import.meta.env.BASE_URL}managements-order`,
        type: "link",
        active: false,
        selected: false,
        dirchange: false,
        title: "Orders",
        roles: ["super_admin", "admin"],
        permissions: [`${ACTION.READ}:${RESOURCE.ORDER}`],
      },
    ],
    roles: ["super_admin", "admin"],
  },
  {
    title: "Products",
    icon: manageProductIcon,
    type: "sub",
    active: false,
    selected: false,
    dirchange: false,
    children: [
      {
        path: `${import.meta.env.BASE_URL}managements-product-categories`,
        type: "link",
        active: false,
        selected: false,
        dirchange: false,
        title: "Product Category",
        roles: ["super_admin", "admin"],
        permissions: [`${ACTION.READ}:${RESOURCE.PRODUCT}`],
      },
      {
        path: `${import.meta.env.BASE_URL}managements-collections`,
        type: "link",
        active: false,
        selected: false,
        dirchange: false,
        title: "Collection",
        roles: ["super_admin", "admin"],
        permissions: [ACTION.READ + ":" + RESOURCE.COLLECTION],
      },
      {
        path: `${import.meta.env.BASE_URL}managements-products`,
        type: "link",
        active: false,
        selected: false,
        dirchange: false,
        title: "Product",
        roles: ["super_admin", "admin"],
        permissions: [`${ACTION.READ}:${RESOURCE.PRODUCT}`],
      },
      {
        path: `${import.meta.env.BASE_URL}managements-products-approval`,
        type: "link",
        active: false,
        selected: false,
        dirchange: false,
        title: "Product Approval",
        roles: ["super_admin", "admin"],
        permissions: [`${ACTION.READ}:${RESOURCE.PRODUCT}`],
      },
      {
        path: `${import.meta.env.BASE_URL}managements-products-organization`,
        type: "link",
        active: false,
        selected: false,
        dirchange: false,
        title: "Product Organization",
        roles: ["super_admin", "admin"],
        permissions: [`${ACTION.READ}:${RESOURCE.PRODUCT}`],
      },
    ],
    roles: ["super_admin", "admin"],
    // permissions: [ACTION.READ + ":" + RESOURCE.PRODUCT],
  },
  {
    title: "Vendors",
    icon: vendorIcon,
    type: "sub",
    active: false,
    selected: false,
    dirchange: false,
    children: [
      {
        path: `${import.meta.env.BASE_URL}managements-vendors`,
        type: "link",
        active: false,
        selected: false,
        dirchange: false,
        title: "Vendors",
        roles: ["super_admin", "admin"],
        permissions: [`${ACTION.READ}:${RESOURCE.VENDOR}`],
      },
      {
        path: `${import.meta.env.BASE_URL}managements-vendors-approval`,
        type: "link",
        active: false,
        selected: false,
        dirchange: false,
        title: "Vendors Approval",
        roles: ["super_admin", "admin"],
        permissions: [`${ACTION.READ}:${RESOURCE.VENDOR}`],
      },
      {
        path: `${import.meta.env.BASE_URL}managements-vendor-earnings`,
        type: "link",
        active: false,
        selected: false,
        dirchange: false,
        title: "Earnings",
        roles: ["super_admin", "admin"],
        permissions: [`${ACTION.READ}:${RESOURCE.VENDOR}`],
      },
      {
        path: `${import.meta.env.BASE_URL}managements-vendors-settings`,
        type: "link",
        active: false,
        selected: false,
        dirchange: false,
        title: "Settings",
        roles: ["super_admin", "admin"],
        permissions: [`${ACTION.READ}:${RESOURCE.VENDOR}`],
      },
    ],
    roles: ["super_admin", "admin"],
  },
  {
    title: "Warehouses",
    icon: manageWarehouseIcon,
    type: "sub",
    active: false,
    selected: false,
    dirchange: false,
    children: [
      {
        path: `${import.meta.env.BASE_URL}managements-warehouses`,
        type: "link",
        active: false,
        selected: false,
        dirchange: false,
        title: "Warehouse",
        roles: ["super_admin", "admin"],
        // permissions: [`${ACTION.READ}:${RESOURCE.PRODUCT}`],
      },
      {
        path: `${import.meta.env.BASE_URL}managements-suppliers`,
        type: "link",
        active: false,
        selected: false,
        dirchange: false,
        title: "Supplier",
        roles: ["super_admin", "admin"],
        // permissions: [`${ACTION.READ}:${RESOURCE.PRODUCT}`],
      },
      {
        path: `${import.meta.env.BASE_URL}managements-inventory-movements`,
        type: "link",
        active: false,
        selected: false,
        dirchange: false,
        title: "Inventory Movements",
        roles: ["super_admin", "admin"],
        // permissions: [`${ACTION.READ}:${RESOURCE.PRODUCT}`],
      },
    ],
    // roles: ["super_admin", "admin"],
    // permissions: [ACTION.READ + ":" + RESOURCE.PRODUCT],
  },
  {
    title: "Marketing",
    icon: rewardPoint,
    type: "sub",
    active: false,
    selected: false,
    dirchange: false,
    children: [
      {
        path: `${import.meta.env.BASE_URL}managements-reward-programs`,
        type: "link",
        active: false,
        selected: false,
        dirchange: false,
        title: "Reward Programs",
        roles: ["super_admin", "admin"],
        permissions: [ACTION.READ + ":" + RESOURCE.REWARD_POINT],
      },
      {
        path: `${import.meta.env.BASE_URL}managements-reward-points`,
        type: "link",
        active: false,
        selected: false,
        dirchange: false,
        title: "Reward Points",
        roles: ["super_admin", "admin"],
        permissions: [ACTION.READ + ":" + RESOURCE.REWARD_POINT],
      },
      {
        path: `${import.meta.env.BASE_URL}managements-campaigns`,
        type: "link",
        active: false,
        selected: false,
        dirchange: false,
        title: "Campaigns",
        roles: ["super_admin", "admin"],
        permissions: [ACTION.READ + ":" + RESOURCE.CAMPAIGN],
      },
      {
        path: `${import.meta.env.BASE_URL}managements-coupon`,
        type: "link",
        active: false,
        selected: false,
        dirchange: false,
        title: "Coupon",
        roles: ["super_admin", "admin"],
        permissions: [ACTION.READ + ":" + RESOURCE.COUPON],
      },
      {
        path: `${import.meta.env.BASE_URL}managements-gift`,
        type: "link",
        active: false,
        selected: false,
        dirchange: false,
        title: "Gift",
        roles: ["super_admin", "admin"],
        permissions: [ACTION.READ + ":" + RESOURCE.GIFT],
      },
      {
        path: `${import.meta.env.BASE_URL}managements-event`,
        type: "link",
        active: false,
        selected: false,
        dirchange: false,
        title: "Event",
        roles: ["super_admin", "admin"],
        permissions: [ACTION.READ + ":" + RESOURCE.EVENT],
      },
      {
        path: `${
          import.meta.env.BASE_URL
        }managements-salon-construction-service`,
        type: "link",
        active: false,
        selected: false,
        dirchange: false,
        title: "Salon Construction Service",
        roles: ["super_admin", "admin"],
        permissions: [ACTION.READ + ":" + RESOURCE.SALON_CONSTRUCTION_SERVICE],
      },
    ],
    roles: ["super_admin", "admin"],
  },
  {
    title: "Report",
    icon: reportIcon,
    type: "sub",
    active: false,
    selected: false,
    dirchange: false,
    children: [
      {
        path: `${import.meta.env.BASE_URL}managements-report/sale`,
        type: "link",
        active: false,
        selected: false,
        dirchange: false,
        title: "Sale Report",
        roles: ["super_admin", "admin"],
        permissions: [ACTION.READ + ":" + RESOURCE.SALE_REPORT],
      },
      {
        path: `${import.meta.env.BASE_URL}managements-report/order`,
        type: "link",
        active: false,
        selected: false,
        dirchange: false,
        title: "Order Report",
        roles: ["super_admin", "admin"],
        permissions: [ACTION.READ + ":" + RESOURCE.ORDER_REPORT],
      },
      {
        path: `${import.meta.env.BASE_URL}managements-report/inventory`,
        type: "link",
        active: false,
        selected: false,
        dirchange: false,
        title: "Inventory Report",
        roles: ["super_admin", "admin"],
        permissions: [ACTION.READ + ":" + RESOURCE.INVENTORY_REPORT],
      },
      {
        path: `${import.meta.env.BASE_URL}managements-report/barcode`,
        type: "link",
        active: false,
        selected: false,
        dirchange: false,
        title: "Barcode",
        roles: ["super_admin", "admin"],
        permissions: [ACTION.READ + ":" + RESOURCE.BARCODE_REPORT],
      },
      {
        path: `${import.meta.env.BASE_URL}managements-report/shipping-checkout`,
        type: "link",
        active: false,
        selected: false,
        dirchange: false,
        title: "Shipping Checkout",
        roles: ["super_admin", "admin"],
        permissions: [ACTION.READ + ":" + RESOURCE.SHIPPING_CHECKOUT_REPORT],
      },
    ],
    roles: ["super_admin", "admin"],
  },
  {
    title: "Store",
    icon: storeIcon,
    type: "sub",
    active: false,
    selected: false,
    dirchange: false,
    children: [
      {
        path: `${import.meta.env.BASE_URL}managements-store`,
        type: "link",
        active: false,
        selected: false,
        dirchange: false,
        title: "Store Management",
        roles: ["super_admin", "admin"],
        permissions: [`${ACTION.READ}:${RESOURCE.STORE}`],
      },
      {
        path: `${import.meta.env.BASE_URL}managements-store/claim-store`,
        type: "link",
        active: false,
        selected: false,
        dirchange: false,
        title: "Claim Store Request",
        roles: ["super_admin", "admin"],
        permissions: [`${ACTION.READ}:${RESOURCE.CLAIM_STORE_REQUEST}`],
      },
    ],
    roles: ["super_admin", "admin"],
  },
  {
    title: "Marketplace",
    icon: manageClassifiedIcon,
    type: "sub",
    active: false,
    selected: false,
    dirchange: false,
    children: [
      {
        path: `${import.meta.env.BASE_URL}managements-marketplace/post`,
        type: "link",
        active: false,
        selected: false,
        dirchange: false,
        title: "Post",
        roles: ["super_admin", "admin"],
        permissions: [ACTION.READ + ":" + RESOURCE.POST],
      },
      {
        path: `${import.meta.env.BASE_URL}managements-marketplace/zurno-post`,
        type: "link",
        active: false,
        selected: false,
        dirchange: false,
        title: "Zurno's Post",
        roles: ["super_admin", "admin"],
        permissions: [`${ACTION.READ}:${RESOURCE.ZURNO_POST}`],
      },
      {
        path: `${import.meta.env.BASE_URL}managements-marketplace/stream-post`,
        type: "link",
        active: false,
        selected: false,
        dirchange: false,
        title: "Live Stream",
        roles: ["super_admin", "admin"],
        permissions: [`${ACTION.READ}:${RESOURCE.STREAM_POST}`],
      },
      {
        path: `${import.meta.env.BASE_URL}managements-marketplace/category`,
        type: "link",
        active: false,
        selected: false,
        dirchange: false,
        title: "Category",
        roles: ["super_admin", "admin"],
        permissions: [ACTION.READ + ":" + RESOURCE.POST_CATEGORY],
      },
    ],
    roles: ["super_admin", "admin"],
    // permissions: [
    //   ACTION.READ + ":" + RESOURCE.POST,
    //   ACTION.READ + ":" + RESOURCE.POST_CATEGORY,
    // ],
  },
  {
    path: `${import.meta.env.BASE_URL}managements-users`,
    icon: manageUser,
    type: "link",
    active: false,
    selected: false,
    dirchange: false,
    title: "Client/User",
    roles: ["super_admin", "admin"],
    permissions: [ACTION.READ + ":" + RESOURCE.USER],
  },

  {
    menutitle: "ADMIN",
    roles: ["super_admin", "admin"],
    permissions: [
      ACTION.READ + ":" + RESOURCE.ADMIN,
      ACTION.READ + ":" + RESOURCE.ROLE,
    ],
  },
  {
    path: `${import.meta.env.BASE_URL}managements-admins`,
    icon: iconUser,
    type: "link",
    active: false,
    selected: false,
    dirchange: false,
    title: "Manage Admins",
    roles: ["super_admin", "admin"],
    permissions: [ACTION.READ + ":" + RESOURCE.ADMIN],
  },
  {
    path: `${import.meta.env.BASE_URL}managements-roles`,
    icon: roleIcon,
    type: "link",
    active: false,
    selected: false,
    dirchange: false,
    title: "Manage Roles",
    roles: ["super_admin", "admin"],
    permissions: [ACTION.READ + ":" + RESOURCE.ROLE],
  },
  {
    path: `${import.meta.env.BASE_URL}managements-permissions-groups`,
    icon: permissionIcon,
    type: "link",
    active: false,
    selected: false,
    dirchange: false,
    title: "Permissions Groups",
    roles: ["super_admin"],
  },
  {
    menutitle: "MOBILE APP",
    roles: ["super_admin", "admin"],
  },
  {
    title: "Mobile App Setting",
    icon: mobileSettingIcon,
    type: "sub",
    active: false,
    selected: false,
    dirchange: false,
    children: [
      {
        path: `${import.meta.env.BASE_URL}managements-home-layout/banners`,
        type: "link",
        active: false,
        selected: false,
        dirchange: false,
        title: "Banner",
        roles: ["super_admin", "admin"],
        permissions: [ACTION.READ + ":" + RESOURCE.BANNER],
      },
      {
        path: `${
          import.meta.env.BASE_URL
        }managements-home-layout/top-categories`,
        type: "link",
        active: false,
        selected: false,
        dirchange: false,
        title: "Top Categories",
        roles: ["super_admin", "admin"],
        permissions: [ACTION.READ + ":" + RESOURCE.TOP_CATEGORIES],
      },
      {
        path: `${
          import.meta.env.BASE_URL
        }managements-home-layout/products-categories`,
        type: "link",
        active: false,
        selected: false,
        dirchange: false,
        title: "Products Categories",
        roles: ["super_admin", "admin"],
        permissions: [ACTION.READ + ":" + RESOURCE.PRODUCTS_CATEGORIES],
      },
      {
        path: `${import.meta.env.BASE_URL}managements-drawer-menus`,
        type: "link",
        active: false,
        selected: false,
        dirchange: false,
        title: "Drawer Menu",
        roles: ["super_admin", "admin"],
        permissions: [ACTION.READ + ":" + RESOURCE.DRAWER_MENU],
      },
      {
        path: `${import.meta.env.BASE_URL}managements-contact-informations`,
        type: "link",
        active: false,
        selected: false,
        dirchange: false,
        title: "Contact Page",
        roles: ["super_admin", "admin"],
        permissions: [ACTION.READ + ":" + RESOURCE.CONTACT_INFORMATION],
      },
      {
        path: `${import.meta.env.BASE_URL}managements-nail-systems`,
        type: "link",
        active: false,
        selected: false,
        dirchange: false,
        title: "Nail System",
        roles: ["super_admin", "admin"],
        permissions: [ACTION.READ + ":" + RESOURCE.NAIL_SYSTEM],
      },
      {
        path: `${import.meta.env.BASE_URL}managements-cancel-reasons`,
        type: "link",
        active: false,
        selected: false,
        dirchange: false,
        title: "Cancel Reasons",
        roles: ["super_admin", "admin"],
        permissions: [ACTION.READ + ":" + RESOURCE.CANCEL_REASON],
      },
      {
        path: `${import.meta.env.BASE_URL}managements-shop-categories`,
        type: "link",
        active: false,
        selected: false,
        dirchange: false,
        title: "Shop Categories",
        roles: ["super_admin", "admin"],
        permissions: [ACTION.READ + ":" + RESOURCE.SHOP_CATEGORY],
      },
      {
        path: `${import.meta.env.BASE_URL}managements-shop-collections`,
        type: "link",
        active: false,
        selected: false,
        dirchange: false,
        title: "Hot Collections",
        roles: ["super_admin", "admin"],
        permissions: [ACTION.READ + ":" + RESOURCE.SHOP_COLLECTION],
      },
    ],
    roles: ["super_admin", "admin"],
  },
  {
    menutitle: "AI",
    roles: ["super_admin", "admin"],
  },
  {
    title: "Chatbot",
    icon: chatbotIcon,
    type: "link",
    path: `${import.meta.env.BASE_URL}chatbot`,
    active: false,
    selected: false,
    dirchange: false,
    roles: ["super_admin", "admin"],
  },
];
