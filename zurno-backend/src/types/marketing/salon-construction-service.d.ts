// Salon Construction Service Types

enum ESalonConstructionServiceStatus {
  PENDING = "pending",
  APPROVED = "approved",
  REJECTED = "rejected",
}

enum EServiceInterest {
  FULL_RENOVATION = "full_renovation",
  PARTIAL_REMODEL = "partial_remodel",
  NEW_CONSTRUCTION = "new_construction",
  ELECTRICAL_PLUMBING = "electrical_plumbing",
  INTERIOR_DESIGN = "interior_design",
}

enum EBudgetRange {
  RANGE_30_50K = "$30-50K",
  RANGE_50_100K = "$50-100K",
  RANGE_100_250K = "$100-250K",
  RANGE_250_500K = "$250-500K",
  RANGE_500K_PLUS = "$500K+",
}

const SERVICE_INTEREST_LABELS = {
  [EServiceInterest.FULL_RENOVATION]: "Full Renovation",
  [EServiceInterest.PARTIAL_REMODEL]:
    "Partial Remodel (e.g. chairs, counters, flooring)",
  [EServiceInterest.NEW_CONSTRUCTION]: "New Construction",
  [EServiceInterest.ELECTRICAL_PLUMBING]: "Electrical / Plumbing Updates",
  [EServiceInterest.INTERIOR_DESIGN]: "Interior Design Services",
};

const BUDGET_RANGE_LABELS = {
  [EBudgetRange.RANGE_30_50K]: "$30-50K",
  [EBudgetRange.RANGE_50_100K]: "$50-100K",
  [EBudgetRange.RANGE_100_250K]: "$100-250K",
  [EBudgetRange.RANGE_250_500K]: "$250-500K",
  [EBudgetRange.RANGE_500K_PLUS]: "$500K+",
};

const STATUS_LABELS = {
  [ESalonConstructionServiceStatus.PENDING]: "Pending",
  [ESalonConstructionServiceStatus.APPROVED]: "Approved",
  [ESalonConstructionServiceStatus.REJECTED]: "Rejected",
};

const STATUS_COLORS = {
  [ESalonConstructionServiceStatus.PENDING]: "warning",
  [ESalonConstructionServiceStatus.APPROVED]: "success",
  [ESalonConstructionServiceStatus.REJECTED]: "danger",
};

type TSalonConstructionServicePdfFile = {
  id: string;
  url: string;
  fileKey: string;
};

type TSalonConstructionServiceSignup = {
  id: string;
  fullName: string;
  businessName: string;
  salonAddress: string;
  phoneNumber: string;
  emailAddress: string;
  serviceInterest: EServiceInterest[];
  preferredStartDate?: string;
  budgetRange: EBudgetRange;
  additionalNotes?: string;
  consentConfirmed: boolean;
  signature?: string;
  signatureDate?: string;
  status: ESalonConstructionServiceStatus;
  pdfFile?: TSalonConstructionServicePdfFile;
  pdfFileId?: string;
  createdAt: string;
  updatedAt: string;
};

type TSalonConstructionServiceCreateRequest = {
  fullName: string;
  businessName: string;
  salonAddress: string;
  phoneNumber: string;
  emailAddress: string;
  serviceInterest: EServiceInterest[];
  preferredStartDate?: string;
  budgetRange: EBudgetRange;
  additionalNotes?: string;
  consentConfirmed: boolean;
  signature?: string;
  signatureDate?: string;
};

type TSalonConstructionServiceUpdateRequest = {
  status: ESalonConstructionServiceStatus;
  rejectionReason?: string;
};

type TSalonConstructionServiceListParams = {
  page?: number;
  limit?: number;
  status?: ESalonConstructionServiceStatus;
  search?: string;
  startDate?: string;
  endDate?: string;
  budgetRange?: EBudgetRange;
};

type TSalonConstructionServiceListResponse = {
  success: boolean;
  data: {
    data: TSalonConstructionServiceSignup[];
    meta: {
      total: number;
      perPage: number;
      currentPage: number;
      lastPage: number;
      firstPage: number;
      firstPageUrl: string;
      lastPageUrl: string;
      nextPageUrl: string | null;
      previousPageUrl: string | null;
    };
  };
};

type TSalonConstructionServiceResponse = {
  success: boolean;
  message?: string;
  data: TSalonConstructionServiceSignup;
};

type TSalonConstructionServiceStatsResponse = {
  success: boolean;
  data: {
    total: number;
    pending: number;
    approved: number;
    rejected: number;
  };
};

type TSalonConstructionServiceErrorResponse = {
  success: false;
  message: string;
  errors?: Array<{
    message: string;
    rule: string;
    field: string;
  }>;
};

// Filter options for the management interface
type TSalonConstructionServiceFilters = {
  status: ESalonConstructionServiceStatus | "all";
  search: string;
  startDate: string;
  endDate: string;
  budgetRange: EBudgetRange | "all";
  page: number;
  limit: number;
};

// Table column configuration
type TSalonConstructionServiceTableColumn = {
  key: string;
  label: string;
  sortable?: boolean;
  width?: string;
  render?: (
    value: any,
    record: TSalonConstructionServiceSignup
  ) => React.ReactNode;
};
